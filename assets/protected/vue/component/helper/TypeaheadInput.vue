<template>
  <div :class="cssStyle">
    <debounced-input
      :disabled="disabled"
      :placeholder="placeholder"
      :callback="update"
      :loading="loading"
      :min-chars="minChars"
      :disableReset="disableReset"
      @reset="reset"
      @keydown.down="down"
      @keydown.up="up"
      @keydown.enter="hit"
      :delay="delay">
    </debounced-input>
    <div class="resultsContainer">
      <ul v-show="hasItems || nothingFound" class="results form-group">
        <slot
          v-if="limited"
          name="limited"
          :items_count="items.length"
          :max_items="limit"
          :max_items_server="serverTotalCount">
          <li v-if="limitText !== ''" class="limited" v-html="limitText"></li>
        </slot>
        <slot v-if="nothingFound" name="empty">
          <li class="nothingFound" v-html="nothingFoundText"></li>
        </slot>
        <li v-for="(item, $item) in items" :class="activeClass($item)" @mousedown="hit" @mousemove="setActive($item)">
          <slot
            name="item"
            :item="item"
            :$item="$item"
            :items_count="items.length"
            :attributes="responseAttr"
            :highlightQuery="highlightQuery"
            :languagesToFlags="languagesToFlags">
            <span v-for="attr in responseAttr" :class="attr">
              <b v-if="responseAttr.length > 1" v-text="attr + ': '"></b>
              <span v-if="attr === 'languages'" v-html="languagesToFlags(item[attr])"></span>
              <span
                v-else
                :class="{ isDir: item['isDir'], isFile: item['isFile'] }"
                v-html="highlightQuery(item[attr])"></span>
            </span>
          </slot>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import Axios from 'axios';
import DebouncedInput from './DebouncedInput.vue';
import { useLanguageHelpers } from '@assets/protected/vue/composables/useLanguageHelpers';

interface Props {
  // URL für AJAX-Request
  src?: string | null;
  // AJAX-Request Parameter
  queryParamName?: string;
  placeholder?: string;
  // Array mit anzuzeigenen Attributen der AJAX-Response
  responseAttr?: Array<string>;
  // Anzahl der anzuzeigenden Elemente in der Ergebnisliste
  limit?: number | null;
  // Dieser Text wird angezeigt, wenn die Ergebnisliste limitiert wurde
  limitText?: string;
  // Dieser Text wird angezeigt, wenn die Ergebnisliste leer ist
  nothingFoundText?: string;
  // Mindestanzahl an Buchstaben, bevor AJAX-Request gestartet wird
  minChars?: number;
  // Zeit (in ms) in der auf weitere Tastenanschläge gewartet wird, bevor Callback aufgerufen wird
  delay?: number;
  // Input-Feld der Komponente disablen
  disabled?: boolean;
  // Attributname der Antwort, in welchem steht, ob einzelne Listeneinträge disabled werden sollen
  enableItemAttr?: string;
  // bei True wird das Input Feld nicht geleert sobald man es verlässt
  disableReset?: boolean;
  // CSS Style
  cssStyle?: string;
  serverLimited?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  src: null,
  queryParamName: 'search',
  placeholder: '...',
  responseAttr: () => ['name'],
  limit: null,
  limitText: '',
  nothingFoundText: '',
  minChars: 3,
  delay: 250,
  disabled: false,
  enableItemAttr: 'enabled',
  disableReset: false,
  cssStyle: 'default',
  serverLimited: false,
});

/**
 * Events:
 * @hit - wird getriggert, wenn auf ein Item innerhalb der Liste geklickt wird (parameter: item)
 * @response - wird getriggert, sobald eine Antwort vom Server vorliegt
 */
const emit = defineEmits<{
  hit: [item: any];
  response: [];
}>();

const { languagesToFlags } = useLanguageHelpers();

const loading = ref<boolean>(false);
const items = ref<Array<JSON>>([]);
const query = ref<string>('');
const lastQuery = ref<string>('');
const current = ref<number>(-1);
const selectFirst = ref<boolean>(false);
const data = ref<any>(null);
const $http = ref<any>(null);
const limited = ref<boolean>(false);
const nothingFound = ref<boolean>(false);
const serverTotalCount = ref<number>(0);

watch(
  () => props.src,
  (value: any) => {
    if (lastQuery.value !== '') update(lastQuery.value);
  },
);

onMounted(() => {
  $http.value = Axios;
});

const highlightQuery = (attr: string): string => {
  // Wenn es nichts zu ersetzen gibt
  if (attr === null) return attr;

  let regex = new RegExp(query.value, 'gi');

  return attr.replace(regex, '<span style="font-weight: bold;">$&</span>');
};

const hasItems = computed(() => {
  return items.value.length > 0;
});

const update = (queryValue: string): void => {
  query.value = queryValue;
  lastQuery.value = query.value;
  cancel();
  loading.value = true;
  let promise = fetch();

  promise.then((response: any) => {
    if (response && query.value) {
      let responseData = response.data;
      console.log('responseData', responseData);
      if (props.serverLimited) serverTotalCount.value = responseData.pop();
      nothingFound.value = responseData.length === 0;
      limited.value = props.serverLimited
        ? (props.limit || 0) < serverTotalCount.value
        : responseData.length > (props.limit || 0);
      items.value = props.limit ? responseData.slice(0, props.limit) : responseData;
      current.value = -1;
      loading.value = false;
      emit('response');
      if (selectFirst.value) {
        down();
      }
    }
  });
};

const fetch = async (): Promise<any> => {
  if (!$http.value) {
    return console.log('You need to provide a HTTP client');
  }

  if (!props.src) {
    return console.log('You need to set the `src` property');
  }

  const src = props.queryParamName ? props.src : props.src + query.value;

  const params = props.queryParamName
    ? (<any>Object).assign({ [props.queryParamName]: query.value }, data.value)
    : data.value;

  return await $http.value.get(src, { params });
};

const cancel = (): void => {
  // used to 'cancel' previous searches
};

const reset = (): void => {
  loading.value = false;
  items.value = [];
  nothingFound.value = false;
  query.value = '';
};

const setActive = (index: number): void => {
  current.value = index;
};

const activeClass = (index: number) => {
  return {
    active: current.value === index,
    disabled: (<any>items.value)[index][props.enableItemAttr] === false,
  };
};

const hit = (): void => {
  if (current.value !== -1 && (<any>items.value)[current.value][props.enableItemAttr] !== false) {
    emit('hit', items.value[current.value]);
  }
  items.value = [];
};

const up = (): void => {
  if (current.value > 0) {
    current.value--;
  } else if (current.value === -1) {
    current.value = items.value.length - 1;
  } else {
    current.value = -1;
  }
};

const down = (): void => {
  if (current.value < items.value.length - 1) {
    current.value++;
  } else {
    current.value = -1;
  }
};
</script>

<style lang="scss" scoped>
@import '@assets/public/vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables.scss';

.resultsContainer {
  position: relative;

  .results {
    position: absolute;
    right: 0;
    top: 5px;
    z-index: 100;
    overflow-y: auto;
    overflow-x: hidden;

    li {
      cursor: pointer;
      list-style: none;
    }
  }
}

div.default {
  .resultsContainer {
    .results {
      padding: 5px;
      color: #e6e6e6;
      background-color: rgba(50, 50, 50, 0.7);
      display: block;
      max-height: 50vh;
      border-radius: 5px;
      border: #808080 1px solid;
      box-shadow: -5px 7px 25px 0px rgba(0, 0, 0, 0.5);

      li {
        position: relative;
        display: block;
        min-width: fit-content;
        white-space: nowrap;

        padding: 8px;
        margin: 3px;
        background-color: rgba(60, 60, 60, 0.95);

        border-radius: 5px;
        border: #808080 1px solid;

        span:first-letter {
          text-transform: capitalize;
        }
      }

      .active {
        background-color: rgba(80, 80, 80, 1);
        box-shadow: inset 0 0 2px #e6e6e6;
      }

      .disabled {
        cursor: not-allowed;
        background-color: #9b0000;
      }
    }
  }
}

div.box {
  .resultsContainer {
    left: -10px;

    ul.results {
      padding-left: 0;
      margin-top: 16px;
      background-color: rgba(50, 50, 50, 0.9);
      min-width: 900px;
      max-height: 80vh;

      li {
        padding: 3px 10px;
        border-bottom: #6b6b6b 1px solid;

        &.limited,
        &.limited:hover,
        &.nothingFound,
        &.nothingFound:hover {
          background-color: $brand-danger;
          padding: 10px;
          color: $white;
          cursor: default;
        }

        span {
          b {
            display: none;
          }

          &.cleanName,
          &.filenameURLDecoded {
            .isDir {
              margin-right: 10px;
            }
          }

          &.extension {
            margin-right: 10px;

            .isFile:before {
              content: '.';
            }
          }

          &.cleanPath,
          &.pathOnlyURLDecoded {
            clear: both;
            display: block;
            font-size: 0.8rem;
            color: #dfdfdf;
          }
        }

        &:hover {
          background-color: $abus-yellow-light;
          color: #333333;

          span.cleanPath,
          span.pathOnlyURLDecoded {
            color: #333333;
          }
        }
      }
    }

    .limitSlot {
      padding: 10px !important;
      background-color: #d9edf7 !important;
      color: #333333 !important;
      font-size: 0.9rem;
    }
  }
}
</style>

<style lang="scss">
div.box {
  .resultsContainer {
    ul.results {
      li {
        span.flags {
          img {
            margin-top: -3px;
          }
        }
      }
    }

    .limitSlot {
      b {
        font-weight: bold;
      }
    }
  }
}
</style>
